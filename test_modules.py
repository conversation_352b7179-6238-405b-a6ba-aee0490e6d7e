#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模块测试脚本
测试各个模块是否正常工作
"""

import sys
import os

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from game_database import GameDatabase
        print("✓ game_database 模块导入成功")
    except Exception as e:
        print(f"✗ game_database 模块导入失败: {e}")
        return False
    
    try:
        from game_scanner import GameScanner
        print("✓ game_scanner 模块导入成功")
    except Exception as e:
        print(f"✗ game_scanner 模块导入失败: {e}")
        return False
    
    try:
        from icon_extractor import IconExtractor
        print("✓ icon_extractor 模块导入成功")
    except Exception as e:
        print(f"✗ icon_extractor 模块导入失败: {e}")
        return False
    
    return True

def test_game_database():
    """测试游戏数据库"""
    print("\n测试游戏数据库...")
    
    try:
        from game_database import GameDatabase
        db = GameDatabase()
        
        # 测试获取已知游戏列表
        games = db.get_all_known_games()
        print(f"✓ 数据库包含 {len(games)} 个已知游戏")
        
        # 测试游戏识别
        test_cases = [
            ("League of Legends.exe", "C:/Riot Games/League of Legends/League of Legends.exe"),
            ("csgo.exe", "C:/Steam/steamapps/common/Counter-Strike Global Offensive/csgo.exe"),
            ("unknown_game.exe", "C:/Games/unknown_game.exe")
        ]
        
        for exe_name, exe_path in test_cases:
            result = db.identify_game(exe_path, exe_name)
            if result:
                print(f"✓ 识别游戏: {exe_name} -> {result['name']} (置信度: {result['confidence']})")
            else:
                print(f"- 未识别游戏: {exe_name}")
        
        return True
        
    except Exception as e:
        print(f"✗ 游戏数据库测试失败: {e}")
        return False

def test_icon_extractor():
    """测试图标提取器"""
    print("\n测试图标提取器...")
    
    try:
        from icon_extractor import IconExtractor
        extractor = IconExtractor()
        
        # 测试创建默认图标
        default_icon = extractor._create_default_icon((32, 32))
        if default_icon:
            print("✓ 默认图标创建成功")
        else:
            print("✗ 默认图标创建失败")
            return False
        
        # 测试Windows API可用性
        if hasattr(extractor, '_extract_icon_windows'):
            print("✓ Windows API图标提取功能可用")
        else:
            print("- Windows API图标提取功能不可用")
        
        return True
        
    except Exception as e:
        print(f"✗ 图标提取器测试失败: {e}")
        return False

def test_game_scanner():
    """测试游戏扫描器"""
    print("\n测试游戏扫描器...")
    
    try:
        from game_scanner import GameScanner
        scanner = GameScanner()
        
        # 测试获取可用驱动器
        drives = scanner.get_available_drives()
        print(f"✓ 找到 {len(drives)} 个可用驱动器: {drives}")
        
        # 测试优先扫描目录
        if drives:
            priority_dirs = scanner.get_priority_scan_dirs(drives[0])
            print(f"✓ 驱动器 {drives[0]} 有 {len(priority_dirs)} 个优先扫描目录")
        
        return True
        
    except Exception as e:
        print(f"✗ 游戏扫描器测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n测试依赖包...")
    
    try:
        import tkinter as tk
        print("✓ tkinter 可用")
    except Exception as e:
        print(f"✗ tkinter 不可用: {e}")
        return False
    
    try:
        from PIL import Image
        print("✓ Pillow 可用")
    except Exception as e:
        print(f"✗ Pillow 不可用: {e}")
        return False
    
    try:
        import psutil
        print("✓ psutil 可用")
    except Exception as e:
        print(f"✗ psutil 不可用: {e}")
        return False
    
    if sys.platform == 'win32':
        try:
            import win32api
            print("✓ pywin32 可用")
        except Exception as e:
            print(f"✗ pywin32 不可用: {e}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("=" * 50)
    print("游戏启动器模块测试")
    print("=" * 50)
    
    tests = [
        ("依赖包测试", test_dependencies),
        ("模块导入测试", test_imports),
        ("游戏数据库测试", test_game_database),
        ("图标提取器测试", test_icon_extractor),
        ("游戏扫描器测试", test_game_scanner)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'-' * 30}")
        print(f"运行: {test_name}")
        print(f"{'-' * 30}")
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print(f"\n{'=' * 50}")
    print(f"测试结果: {passed}/{total} 通过")
    print(f"{'=' * 50}")
    
    if passed == total:
        print("🎉 所有测试通过！程序应该可以正常运行。")
        return True
    else:
        print("⚠️  部分测试失败，程序可能存在问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
