#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图标提取模块
从exe文件中提取图标
"""

import os
import sys
from PIL import Image, ImageTk
import tkinter as tk

# Windows特定的图标提取
if sys.platform == 'win32':
    try:
        import win32api
        import win32con
        import win32gui
        import win32ui
        from win32gui import ExtractIconEx
        WINDOWS_AVAILABLE = True
    except ImportError:
        WINDOWS_AVAILABLE = False
else:
    WINDOWS_AVAILABLE = False


class IconExtractor:
    def __init__(self):
        self.icon_cache = {}  # 图标缓存
        self.default_icon_size = (32, 32)
        
    def extract_icon(self, exe_path, size=(32, 32)):
        """
        从exe文件提取图标
        
        Args:
            exe_path: exe文件路径
            size: 图标尺寸 (width, height)
            
        Returns:
            PIL.Image: 图标图像，失败返回None
        """
        # 检查缓存
        cache_key = f"{exe_path}_{size[0]}x{size[1]}"
        if cache_key in self.icon_cache:
            return self.icon_cache[cache_key]
        
        icon = None
        
        if WINDOWS_AVAILABLE and os.path.exists(exe_path):
            try:
                # 使用Windows API提取图标
                icon = self._extract_icon_windows(exe_path, size)
            except Exception as e:
                print(f"Windows API提取图标失败: {e}")
        
        # 如果Windows API失败，尝试其他方法
        if icon is None:
            icon = self._extract_icon_fallback(exe_path, size)
        
        # 缓存结果
        if icon:
            self.icon_cache[cache_key] = icon
        
        return icon
    
    def _extract_icon_windows(self, exe_path, size):
        """
        使用Windows API提取图标
        
        Args:
            exe_path: exe文件路径
            size: 图标尺寸
            
        Returns:
            PIL.Image: 图标图像
        """
        try:
            # 提取图标句柄
            large_icons, small_icons = ExtractIconEx(exe_path, 0)
            
            if not large_icons and not small_icons:
                return None
            
            # 选择合适的图标
            icon_handle = None
            if size[0] <= 16 and small_icons:
                icon_handle = small_icons[0]
            elif large_icons:
                icon_handle = large_icons[0]
            elif small_icons:
                icon_handle = small_icons[0]
            
            if not icon_handle:
                return None
            
            # 获取图标信息
            icon_info = win32gui.GetIconInfo(icon_handle)
            
            # 创建设备上下文
            hdc = win32ui.CreateDCFromHandle(win32gui.GetDC(0))
            hdc_mem = hdc.CreateCompatibleDC()
            
            # 获取图标尺寸
            try:
                icon_size = win32api.GetSystemMetrics(win32con.SM_CXICON)
                if size[0] <= 16:
                    icon_size = win32api.GetSystemMetrics(win32con.SM_CXSMICON)
            except:
                icon_size = 32  # 默认尺寸
            
            # 创建位图
            hbm = win32ui.CreateBitmap()
            hbm.CreateCompatibleBitmap(hdc, icon_size, icon_size)
            hdc_mem.SelectObject(hbm)
            
            # 绘制图标到位图
            win32gui.DrawIconEx(
                hdc_mem.GetSafeHdc(),
                0, 0,
                icon_handle,
                icon_size, icon_size,
                0, None,
                win32con.DI_NORMAL
            )
            
            # 获取位图数据
            bmp_info = hbm.GetInfo()
            bmp_str = hbm.GetBitmapBits(True)
            
            # 转换为PIL图像
            img = Image.frombuffer(
                'RGB',
                (bmp_info['bmWidth'], bmp_info['bmHeight']),
                bmp_str, 'raw', 'BGRX', 0, 1
            )
            
            # 调整尺寸
            if img.size != size:
                img = img.resize(size, Image.Resampling.LANCZOS)
            
            # 清理资源
            win32gui.DestroyIcon(icon_handle)
            hdc_mem.DeleteDC()
            hdc.DeleteDC()
            
            return img
            
        except Exception as e:
            print(f"Windows API图标提取错误: {e}")
            return None
    
    def _extract_icon_fallback(self, exe_path, size):
        """
        备用图标提取方法
        
        Args:
            exe_path: exe文件路径
            size: 图标尺寸
            
        Returns:
            PIL.Image: 图标图像
        """
        try:
            # 尝试查找同目录下的图标文件
            exe_dir = os.path.dirname(exe_path)
            exe_name = os.path.splitext(os.path.basename(exe_path))[0]
            
            # 可能的图标文件名
            icon_names = [
                f"{exe_name}.ico",
                f"{exe_name}.png",
                "icon.ico",
                "icon.png",
                "game.ico",
                "game.png"
            ]
            
            for icon_name in icon_names:
                icon_path = os.path.join(exe_dir, icon_name)
                if os.path.exists(icon_path):
                    try:
                        img = Image.open(icon_path)
                        if img.mode != 'RGBA':
                            img = img.convert('RGBA')
                        
                        if img.size != size:
                            img = img.resize(size, Image.Resampling.LANCZOS)
                        
                        return img
                    except Exception as e:
                        print(f"加载图标文件失败 {icon_path}: {e}")
                        continue
            
            # 如果找不到图标文件，返回默认图标
            return self._create_default_icon(size)
            
        except Exception as e:
            print(f"备用图标提取错误: {e}")
            return self._create_default_icon(size)
    
    def _create_default_icon(self, size):
        """
        创建默认图标
        
        Args:
            size: 图标尺寸
            
        Returns:
            PIL.Image: 默认图标
        """
        try:
            # 创建一个简单的默认图标
            img = Image.new('RGBA', size, (70, 130, 180, 255))  # 钢蓝色背景
            
            # 在中心绘制一个游戏手柄图标（简化版）
            from PIL import ImageDraw
            draw = ImageDraw.Draw(img)
            
            # 计算中心位置
            center_x, center_y = size[0] // 2, size[1] // 2
            
            # 绘制简单的游戏图标
            # 外框
            margin = size[0] // 8
            draw.rectangle(
                [margin, margin, size[0] - margin, size[1] - margin],
                outline=(255, 255, 255, 255),
                width=2
            )
            
            # 中心点
            dot_size = size[0] // 16
            draw.ellipse(
                [center_x - dot_size, center_y - dot_size, 
                 center_x + dot_size, center_y + dot_size],
                fill=(255, 255, 255, 255)
            )
            
            return img
            
        except Exception as e:
            print(f"创建默认图标失败: {e}")
            # 如果连默认图标都创建失败，返回纯色图标
            return Image.new('RGBA', size, (128, 128, 128, 255))
    
    def extract_icon_to_file(self, exe_path, output_path, size=(32, 32)):
        """
        提取图标并保存到文件
        
        Args:
            exe_path: exe文件路径
            output_path: 输出文件路径
            size: 图标尺寸
            
        Returns:
            bool: 是否成功
        """
        try:
            icon = self.extract_icon(exe_path, size)
            if icon:
                icon.save(output_path)
                return True
            return False
        except Exception as e:
            print(f"保存图标到文件失败: {e}")
            return False
    
    def get_icon_for_tkinter(self, exe_path, size=(32, 32)):
        """
        获取适用于tkinter的图标
        
        Args:
            exe_path: exe文件路径
            size: 图标尺寸
            
        Returns:
            ImageTk.PhotoImage: tkinter图标对象
        """
        try:
            icon = self.extract_icon(exe_path, size)
            if icon:
                return ImageTk.PhotoImage(icon)
            return None
        except Exception as e:
            print(f"创建tkinter图标失败: {e}")
            return None
    
    def clear_cache(self):
        """清空图标缓存"""
        self.icon_cache.clear()
    
    def get_cache_size(self):
        """获取缓存大小"""
        return len(self.icon_cache)
    
    def preload_icons(self, exe_paths, size=(32, 32), progress_callback=None):
        """
        预加载图标到缓存
        
        Args:
            exe_paths: exe文件路径列表
            size: 图标尺寸
            progress_callback: 进度回调函数
        """
        total = len(exe_paths)
        
        for i, exe_path in enumerate(exe_paths):
            try:
                self.extract_icon(exe_path, size)
                
                if progress_callback:
                    progress_callback(f"预加载图标: {i + 1}/{total}")
                    
            except Exception as e:
                print(f"预加载图标失败 {exe_path}: {e}")
    
    def get_multiple_sizes(self, exe_path, sizes=[(16, 16), (32, 32), (48, 48)]):
        """
        获取多种尺寸的图标
        
        Args:
            exe_path: exe文件路径
            sizes: 尺寸列表
            
        Returns:
            dict: 尺寸到图标的映射
        """
        icons = {}
        
        for size in sizes:
            try:
                icon = self.extract_icon(exe_path, size)
                if icon:
                    icons[size] = icon
            except Exception as e:
                print(f"提取图标失败 {exe_path} {size}: {e}")
        
        return icons
    
    def is_valid_icon_file(self, file_path):
        """
        检查文件是否为有效的图标文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否为有效图标文件
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            # 检查文件扩展名
            ext = os.path.splitext(file_path)[1].lower()
            if ext not in ['.ico', '.png', '.jpg', '.jpeg', '.bmp', '.gif']:
                return False
            
            # 尝试打开图像
            with Image.open(file_path) as img:
                return True
                
        except Exception:
            return False
