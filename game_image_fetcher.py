#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏图片获取模块
从网络获取游戏封面图片
"""

import os
import requests
import json
from PIL import Image
from io import BytesIO
import threading
import time


class GameImageFetcher:
    def __init__(self):
        self.image_cache = {}  # 内存缓存
        self.cache_dir = "game_images_cache"  # 本地缓存目录
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 创建缓存目录
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
        
        # 百度图片搜索URL模板
        self.baidu_image_search_url = "https://image.baidu.com/search/acjson?tn=resultjson_com&logid=&ipn=rj&ct=201326592&is=&fp=result&queryWord={}&cl=2&lm=-1&ie=utf-8&oe=utf-8&adpicid=&st=-1&z=&ic=0&hd=&latest=&copyright=&word={}&s=&se=&tab=&width=&height=&face=0&istype=2&qc=&nc=1&fr=&expermode=&force=&pn=0&rn=30"

        # 游戏关键词映射（用于百度搜索）
        self.game_search_keywords = {
            'League of Legends': '英雄联盟 游戏 封面',
            'PUBG': '绝地求生 PUBG 游戏 封面',
            'Counter-Strike 2': 'CS2 反恐精英2 游戏 封面',
            'Counter-Strike: Global Offensive': 'CSGO 反恐精英 游戏 封面',
            'Valorant': 'Valorant 无畏契约 游戏 封面',
            'Apex Legends': 'Apex Legends 游戏 封面',
            'DOTA 2': 'DOTA2 刀塔2 游戏 封面',
            'Genshin Impact': '原神 Genshin Impact 游戏 封面',
            'World of Warcraft': '魔兽世界 WOW 游戏 封面',
            'The Witcher 3': '巫师3 游戏 封面',
            'Minecraft': 'Minecraft 我的世界 游戏 封面',
            'Grand Theft Auto V': 'GTA5 侠盗猎车手5 游戏 封面',
            'Forza Horizon 5': 'Forza Horizon 5 极限竞速 游戏 封面',
            'Age of Empires II': '帝国时代2 游戏 封面',
            'Civilization VI': '文明6 游戏 封面',
            'Call of Duty: Warzone': 'COD 使命召唤 游戏 封面',
            'Steam': 'Steam 平台 图标',
            'Epic Games Launcher': 'Epic Games 平台 图标',
            'Origin': 'Origin EA 平台 图标',
            'Battle.net': 'Battle.net 暴雪 平台 图标',
            'Uplay': 'Uplay 育碧 平台 图标'
        }
        
        # 备用搜索API（如果直接URL失效）
        self.search_apis = [
            'https://api.rawg.io/api/games?key=YOUR_API_KEY&search=',  # RAWG API
            'https://api.igdb.com/v4/games',  # IGDB API
        ]
    
    def get_cached_image_path(self, game_name):
        """获取缓存图片路径"""
        safe_name = "".join(c for c in game_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        return os.path.join(self.cache_dir, f"{safe_name}.jpg")
    
    def download_image(self, url, timeout=10):
        """下载图片"""
        try:
            response = self.session.get(url, timeout=timeout, stream=True)
            response.raise_for_status()
            
            # 检查内容类型
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                return None
            
            # 下载图片数据
            image_data = BytesIO()
            for chunk in response.iter_content(chunk_size=8192):
                image_data.write(chunk)
            
            image_data.seek(0)
            return Image.open(image_data)
            
        except Exception as e:
            print(f"下载图片失败 {url}: {e}")
            return None
    
    def search_game_image_baidu(self, game_name):
        """使用百度图片搜索游戏图片"""
        try:
            # 获取搜索关键词
            if game_name in self.game_search_keywords:
                search_keyword = self.game_search_keywords[game_name]
            else:
                search_keyword = f"{game_name} 游戏 封面"

            # 构建搜索URL
            import urllib.parse
            encoded_keyword = urllib.parse.quote(search_keyword)
            search_url = self.baidu_image_search_url.format(encoded_keyword, encoded_keyword)

            # 发送搜索请求
            response = self.session.get(search_url, timeout=10)
            response.raise_for_status()

            # 解析JSON响应
            data = response.json()
            if 'data' in data and len(data['data']) > 0:
                # 获取前几个图片URL
                for item in data['data'][:5]:  # 尝试前5个图片
                    if 'thumbURL' in item:
                        image_url = item['thumbURL']
                        image = self.download_image(image_url)
                        if image:
                            return image
                    elif 'middleURL' in item:
                        image_url = item['middleURL']
                        image = self.download_image(image_url)
                        if image:
                            return image

            return None

        except Exception as e:
            print(f"百度图片搜索失败 {game_name}: {e}")
            return None
    
    def get_game_image(self, game_name, size=(120, 160)):
        """
        获取游戏图片（优先使用本地生成的图片）

        Args:
            game_name: 游戏名称
            size: 目标尺寸 (width, height)

        Returns:
            PIL.Image: 游戏图片，失败返回None
        """
        # 检查内存缓存
        cache_key = f"{game_name}_{size[0]}x{size[1]}"
        if cache_key in self.image_cache:
            return self.image_cache[cache_key]

        # 检查本地缓存
        cache_path = self.get_cached_image_path(game_name)
        if os.path.exists(cache_path):
            try:
                image = Image.open(cache_path)
                if image.size != size:
                    image = image.resize(size, Image.Resampling.LANCZOS)

                # 转换为RGBA确保兼容性
                if image.mode != 'RGBA':
                    image = image.convert('RGBA')

                self.image_cache[cache_key] = image
                return image
            except Exception as e:
                print(f"加载缓存图片失败 {cache_path}: {e}")
                # 删除损坏的缓存文件
                try:
                    os.remove(cache_path)
                except:
                    pass

        # 尝试从百度搜索获取图片
        image = None
        try:
            image = self.search_game_image_baidu(game_name)
            print(f"尝试从百度获取图片: {game_name}")
        except Exception as e:
            print(f"百度图片搜索失败: {e}")

        # 如果百度搜索失败，创建漂亮的本地图片
        if image is None:
            image = self.create_beautiful_game_image(game_name, size)
            print(f"使用本地生成图片: {game_name}")

        # 保存到本地缓存
        if image:
            try:
                # 转换为RGB保存为JPEG（更小的文件）
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    rgb_image.paste(image, mask=image.split()[-1])
                else:
                    rgb_image.paste(image)

                rgb_image.save(cache_path, 'JPEG', quality=85)
            except Exception as e:
                print(f"保存缓存图片失败 {cache_path}: {e}")

        # 缓存到内存
        if image:
            self.image_cache[cache_key] = image

        return image
    
    def create_beautiful_game_image(self, game_name, size):
        """创建漂亮的游戏图片"""
        try:
            from PIL import ImageDraw, ImageFont

            # 游戏主题色彩映射
            game_colors = {
                'League of Legends': [(30, 60, 114), (100, 149, 237)],
                'PUBG': [(255, 107, 53), (255, 140, 0)],
                'Counter-Strike 2': [(247, 147, 30), (255, 193, 7)],
                'Counter-Strike: Global Offensive': [(247, 147, 30), (255, 193, 7)],
                'Valorant': [(255, 70, 85), (255, 152, 0)],
                'Apex Legends': [(255, 102, 0), (255, 193, 7)],
                'DOTA 2': [(211, 44, 230), (156, 39, 176)],
                'Genshin Impact': [(79, 195, 247), (33, 150, 243)],
                'World of Warcraft': [(0, 132, 255), (63, 81, 181)],
                'The Witcher 3': [(139, 195, 74), (76, 175, 80)],
                'Minecraft': [(76, 175, 80), (139, 195, 74)],
                'Grand Theft Auto V': [(33, 33, 33), (97, 97, 97)],
                'Forza Horizon 5': [(255, 152, 0), (255, 193, 7)],
                'Age of Empires II': [(121, 85, 72), (141, 110, 99)],
                'Civilization VI': [(63, 81, 181), (92, 107, 192)],
                'Call of Duty: Warzone': [(96, 125, 139), (120, 144, 156)],
                'Steam': [(27, 40, 56), (66, 90, 112)],
                'Epic Games Launcher': [(49, 49, 49), (97, 97, 97)],
                'Origin': [(255, 102, 0), (255, 152, 0)],
                'Battle.net': [(0, 174, 255), (33, 150, 243)],
                'Uplay': [(0, 132, 255), (33, 150, 243)]
            }

            # 获取游戏颜色，如果没有则使用默认颜色
            if game_name in game_colors:
                color1, color2 = game_colors[game_name]
            else:
                # 根据游戏名称生成颜色
                hash_val = hash(game_name) % 360
                import colorsys
                rgb1 = colorsys.hsv_to_rgb(hash_val/360, 0.8, 0.9)
                rgb2 = colorsys.hsv_to_rgb((hash_val + 60)/360, 0.6, 0.7)
                color1 = tuple(int(c * 255) for c in rgb1)
                color2 = tuple(int(c * 255) for c in rgb2)

            # 创建渐变背景
            image = Image.new('RGBA', size, color1 + (255,))
            draw = ImageDraw.Draw(image)

            # 创建渐变效果
            for i in range(size[1]):
                ratio = i / size[1]
                r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
                g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
                b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
                draw.line([(0, i), (size[0], i)], fill=(r, g, b, 255))

            # 添加装饰性图案
            self.add_decorative_pattern(draw, size, game_name)

            # 绘制游戏名称
            self.draw_game_title(draw, size, game_name)

            # 添加边框
            draw.rectangle([0, 0, size[0]-1, size[1]-1], outline=(255, 255, 255, 150), width=2)

            return image

        except Exception as e:
            print(f"创建漂亮游戏图片失败: {e}")
            return self.create_default_game_image(game_name, size)

    def add_decorative_pattern(self, draw, size, game_name):
        """添加装饰性图案"""
        try:
            # 根据游戏类型添加不同的装饰
            if any(keyword in game_name.lower() for keyword in ['fps', 'shooter', 'counter', 'pubg', 'valorant', 'apex']):
                # 射击游戏 - 添加准星图案
                center_x, center_y = size[0] // 2, size[1] // 2
                cross_size = min(size[0], size[1]) // 8
                draw.line([(center_x - cross_size, center_y), (center_x + cross_size, center_y)],
                         fill=(255, 255, 255, 100), width=3)
                draw.line([(center_x, center_y - cross_size), (center_x, center_y + cross_size)],
                         fill=(255, 255, 255, 100), width=3)

            elif any(keyword in game_name.lower() for keyword in ['moba', 'league', 'dota']):
                # MOBA游戏 - 添加六边形图案
                center_x, center_y = size[0] // 2, size[1] // 2
                hex_size = min(size[0], size[1]) // 6
                import math
                points = []
                for i in range(6):
                    angle = i * math.pi / 3
                    x = center_x + hex_size * math.cos(angle)
                    y = center_y + hex_size * math.sin(angle)
                    points.append((x, y))
                draw.polygon(points, outline=(255, 255, 255, 120), width=2)

            elif any(keyword in game_name.lower() for keyword in ['rpg', 'genshin', 'witcher', 'warcraft']):
                # RPG游戏 - 添加魔法圆图案
                center_x, center_y = size[0] // 2, size[1] // 2
                radius = min(size[0], size[1]) // 6
                draw.ellipse([center_x - radius, center_y - radius, center_x + radius, center_y + radius],
                           outline=(255, 255, 255, 120), width=2)
                draw.ellipse([center_x - radius//2, center_y - radius//2, center_x + radius//2, center_y + radius//2],
                           outline=(255, 255, 255, 80), width=1)

            else:
                # 默认 - 添加简单的几何图案
                corner_size = min(size[0], size[1]) // 10
                # 左上角
                draw.polygon([(0, 0), (corner_size, 0), (0, corner_size)], fill=(255, 255, 255, 60))
                # 右下角
                draw.polygon([(size[0], size[1]), (size[0] - corner_size, size[1]), (size[0], size[1] - corner_size)],
                           fill=(255, 255, 255, 60))

        except Exception as e:
            print(f"添加装饰图案失败: {e}")

    def draw_game_title(self, draw, size, game_name):
        """绘制游戏标题"""
        try:
            from PIL import ImageFont

            # 尝试使用系统字体
            try:
                font_size = min(size[0] // 10, size[1] // 12, 14)
                font = ImageFont.truetype("arial.ttf", font_size)
                bold_font = ImageFont.truetype("arialbd.ttf", font_size + 2)
            except:
                font = ImageFont.load_default()
                bold_font = font

            # 文本换行处理
            words = game_name.split()
            lines = []
            current_line = ""

            for word in words:
                test_line = current_line + (" " if current_line else "") + word
                bbox = draw.textbbox((0, 0), test_line, font=font)
                if bbox[2] - bbox[0] <= size[0] - 20:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                    current_line = word

            if current_line:
                lines.append(current_line)

            # 限制行数
            if len(lines) > 3:
                lines = lines[:2] + [lines[2][:10] + "..."]

            # 绘制文本
            line_height = font_size + 4
            total_height = len(lines) * line_height
            start_y = size[1] - total_height - 10

            for i, line in enumerate(lines):
                bbox = draw.textbbox((0, 0), line, font=bold_font)
                text_width = bbox[2] - bbox[0]
                x = (size[0] - text_width) // 2
                y = start_y + i * line_height

                # 绘制文本阴影
                draw.text((x + 2, y + 2), line, font=bold_font, fill=(0, 0, 0, 180))
                # 绘制文本
                draw.text((x, y), line, font=bold_font, fill=(255, 255, 255, 255))

        except Exception as e:
            print(f"绘制游戏标题失败: {e}")

    def create_default_game_image(self, game_name, size):
        """创建默认游戏图片"""
        try:
            from PIL import ImageDraw, ImageFont
            
            # 创建渐变背景
            image = Image.new('RGBA', size, (70, 130, 180, 255))
            draw = ImageDraw.Draw(image)
            
            # 添加渐变效果
            for i in range(size[1]):
                alpha = int(255 * (1 - i / size[1] * 0.3))
                color = (70, 130, 180, alpha)
                draw.line([(0, i), (size[0], i)], fill=color)
            
            # 绘制游戏名称
            try:
                # 尝试使用系统字体
                font_size = min(size[0] // 8, size[1] // 10, 16)
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                font = ImageFont.load_default()
            
            # 文本换行处理
            words = game_name.split()
            lines = []
            current_line = ""
            
            for word in words:
                test_line = current_line + (" " if current_line else "") + word
                bbox = draw.textbbox((0, 0), test_line, font=font)
                if bbox[2] - bbox[0] <= size[0] - 20:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                    current_line = word
            
            if current_line:
                lines.append(current_line)
            
            # 绘制文本
            total_height = len(lines) * (font_size + 2)
            start_y = (size[1] - total_height) // 2
            
            for i, line in enumerate(lines):
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                x = (size[0] - text_width) // 2
                y = start_y + i * (font_size + 2)
                
                # 绘制文本阴影
                draw.text((x + 1, y + 1), line, font=font, fill=(0, 0, 0, 128))
                # 绘制文本
                draw.text((x, y), line, font=font, fill=(255, 255, 255, 255))
            
            # 添加游戏图标
            icon_size = min(size[0] // 4, size[1] // 4, 32)
            icon_x = size[0] - icon_size - 10
            icon_y = 10
            
            # 绘制简单的游戏手柄图标
            draw.ellipse([icon_x, icon_y, icon_x + icon_size, icon_y + icon_size], 
                        fill=(255, 255, 255, 200), outline=(255, 255, 255, 255))
            
            # 添加装饰边框
            draw.rectangle([0, 0, size[0]-1, size[1]-1], outline=(255, 255, 255, 100), width=2)
            
            return image
            
        except Exception as e:
            print(f"创建默认游戏图片失败: {e}")
            # 最简单的备用方案
            return Image.new('RGBA', size, (100, 100, 100, 255))
    
    def preload_images(self, game_names, size=(120, 160), progress_callback=None):
        """预加载游戏图片"""
        def load_worker():
            total = len(game_names)
            for i, game_name in enumerate(game_names):
                try:
                    self.get_game_image(game_name, size)
                    if progress_callback:
                        progress_callback(f"预加载图片: {i + 1}/{total} - {game_name}")
                except Exception as e:
                    print(f"预加载图片失败 {game_name}: {e}")
                
                # 避免请求过于频繁
                time.sleep(0.1)
        
        thread = threading.Thread(target=load_worker)
        thread.daemon = True
        thread.start()
    
    def clear_cache(self):
        """清空缓存"""
        # 清空内存缓存
        self.image_cache.clear()
        
        # 清空本地缓存
        try:
            import shutil
            if os.path.exists(self.cache_dir):
                shutil.rmtree(self.cache_dir)
                os.makedirs(self.cache_dir)
        except Exception as e:
            print(f"清空本地缓存失败: {e}")
    
    def get_cache_info(self):
        """获取缓存信息"""
        memory_count = len(self.image_cache)
        
        disk_count = 0
        disk_size = 0
        if os.path.exists(self.cache_dir):
            for filename in os.listdir(self.cache_dir):
                filepath = os.path.join(self.cache_dir, filename)
                if os.path.isfile(filepath):
                    disk_count += 1
                    disk_size += os.path.getsize(filepath)
        
        return {
            'memory_cached': memory_count,
            'disk_cached': disk_count,
            'disk_size_mb': disk_size / (1024 * 1024)
        }
