#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏启动器主程序
自动扫描全盘游戏并提供图形界面启动
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import json
import os
from game_scanner import GameScanner
from icon_extractor import IconExtractor
from game_database import GameDatabase


class GameLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("游戏启动器")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 初始化组件
        self.game_scanner = GameScanner()
        self.icon_extractor = IconExtractor()
        self.game_database = GameDatabase()
        
        # 游戏列表
        self.games = []
        self.filtered_games = []
        
        # 创建界面
        self.create_widgets()
        self.load_games_from_cache()
        
    def create_widgets(self):
        """创建主界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 顶部工具栏
        toolbar = ttk.Frame(main_frame)
        toolbar.pack(fill=tk.X, pady=(0, 10))
        
        # 扫描按钮
        self.scan_btn = ttk.Button(toolbar, text="扫描游戏", command=self.start_scan)
        self.scan_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        refresh_btn = ttk.Button(toolbar, text="刷新列表", command=self.refresh_games)
        refresh_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 搜索框
        search_frame = ttk.Frame(toolbar)
        search_frame.pack(side=tk.RIGHT)
        
        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.filter_games)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=20)
        search_entry.pack(side=tk.LEFT)
        
        # 进度条
        self.progress_var = tk.StringVar(value="就绪")
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(0, 5))
        
        self.status_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.status_label.pack()
        
        # 游戏图标展示框架
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建滚动画布用于显示游戏图标
        self.canvas = tk.Canvas(list_frame, bg='white')
        scrollbar_v = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        scrollbar_h = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)

        self.scrollable_frame = ttk.Frame(self.canvas)
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

        # 布局
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 绑定鼠标滚轮事件
        self.canvas.bind("<MouseWheel>", self._on_mousewheel)
        self.scrollable_frame.bind("<MouseWheel>", self._on_mousewheel)

        # 游戏图标按钮列表
        self.game_buttons = []
        
        # 右键菜单
        self.create_context_menu()
        
    def create_context_menu(self):
        """创建右键菜单"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="启动游戏", command=self.launch_selected_game)
        self.context_menu.add_command(label="打开文件位置", command=self.open_game_location)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="从列表中移除", command=self.remove_game)

    def show_context_menu(self, event, game_data):
        """显示右键菜单"""
        self.selected_game = game_data
        self.context_menu.post(event.x_root, event.y_root)

    def _on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            
    def start_scan(self):
        """开始扫描游戏"""
        self.scan_btn.config(state='disabled')
        self.progress_bar.start()
        self.progress_var.set("正在扫描游戏...")
        
        # 在新线程中执行扫描
        scan_thread = threading.Thread(target=self.scan_games_thread)
        scan_thread.daemon = True
        scan_thread.start()
        
    def scan_games_thread(self):
        """扫描游戏的线程函数"""
        try:
            def progress_callback(message):
                self.root.after(0, lambda: self.progress_var.set(message))
                
            # 执行扫描
            games = self.game_scanner.scan_all_drives(progress_callback)
            
            # 更新界面
            self.root.after(0, lambda: self.scan_complete(games))
            
        except Exception as e:
            self.root.after(0, lambda: self.scan_error(str(e)))
            
    def scan_complete(self, games):
        """扫描完成回调"""
        self.games = games
        self.filtered_games = games.copy()
        self.update_game_list()
        self.save_games_to_cache()
        
        self.progress_bar.stop()
        self.scan_btn.config(state='normal')
        self.progress_var.set(f"扫描完成，找到 {len(games)} 个游戏")
        
    def scan_error(self, error_msg):
        """扫描错误回调"""
        self.progress_bar.stop()
        self.scan_btn.config(state='normal')
        self.progress_var.set("扫描失败")
        messagebox.showerror("错误", f"扫描游戏时出错：{error_msg}")
        
    def update_game_list(self):
        """更新游戏图标显示"""
        # 清空现有按钮
        for button in self.game_buttons:
            button.destroy()
        self.game_buttons.clear()

        # 计算每行显示的图标数量
        canvas_width = self.canvas.winfo_width() if self.canvas.winfo_width() > 1 else 800
        icon_width = 120  # 图标按钮宽度
        icons_per_row = max(1, canvas_width // icon_width)

        # 创建游戏图标按钮
        row = 0
        col = 0

        for game in self.filtered_games:
            # 创建游戏按钮框架
            game_frame = tk.Frame(self.scrollable_frame, relief=tk.RAISED, borderwidth=1,
                                bg='white', cursor='hand2')
            game_frame.grid(row=row, column=col, padx=5, pady=5, sticky='nsew')

            # 游戏图标标签
            icon_label = tk.Label(game_frame, text='🎮', font=('Arial', 24),
                                bg='white', width=6, height=3)
            icon_label.pack(pady=(5, 0))

            # 游戏名称标签
            name_label = tk.Label(game_frame, text=game['name'], font=('Arial', 9),
                                bg='white', wraplength=100, justify='center')
            name_label.pack(pady=(0, 5))

            # 绑定事件
            for widget in [game_frame, icon_label, name_label]:
                widget.bind('<Double-Button-1>', lambda e, g=game: self.launch_game_by_data(g))
                widget.bind('<Button-3>', lambda e, g=game: self.show_context_menu(e, g))
                widget.bind('<Enter>', lambda e, f=game_frame: f.configure(bg='#e6f3ff'))
                widget.bind('<Leave>', lambda e, f=game_frame: f.configure(bg='white'))

            # 异步加载真实图标
            self.load_game_icon_async_new(icon_label, game['path'])

            self.game_buttons.append(game_frame)

            # 更新行列位置
            col += 1
            if col >= icons_per_row:
                col = 0
                row += 1

        # 更新滚动区域
        self.scrollable_frame.update_idletasks()
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
            
    def load_game_icon_async_new(self, icon_label, exe_path):
        """异步加载游戏图标到标签"""
        def load_icon():
            try:
                icon = self.icon_extractor.extract_icon(exe_path, size=(48, 48))
                if icon:
                    # 转换为tkinter可用的格式
                    photo = ImageTk.PhotoImage(icon)
                    self.root.after(0, lambda: self.update_label_icon(icon_label, photo))
            except:
                pass  # 忽略图标加载错误

        thread = threading.Thread(target=load_icon)
        thread.daemon = True
        thread.start()

    def update_label_icon(self, icon_label, photo):
        """更新标签图标"""
        try:
            icon_label.configure(image=photo, text='')
            icon_label.image = photo  # 保持引用防止被垃圾回收
        except:
            pass

    def launch_game_by_data(self, game_data):
        """通过游戏数据启动游戏"""
        try:
            os.startfile(game_data['path'])
            self.progress_var.set(f"已启动游戏: {game_data['name']}")
        except Exception as e:
            messagebox.showerror("错误", f"启动游戏失败：{str(e)}")
        
    def format_file_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
        
    def filter_games(self, *args):
        """过滤游戏列表"""
        search_text = self.search_var.get().lower()
        if not search_text:
            self.filtered_games = self.games.copy()
        else:
            self.filtered_games = [
                game for game in self.games 
                if search_text in game['name'].lower() or search_text in game['path'].lower()
            ]
        self.update_game_list()
        
    def launch_selected_game(self):
        """启动选中的游戏"""
        if hasattr(self, 'selected_game') and self.selected_game:
            try:
                os.startfile(self.selected_game['path'])
                self.progress_var.set(f"已启动游戏: {self.selected_game['name']}")
            except Exception as e:
                messagebox.showerror("错误", f"启动游戏失败：{str(e)}")

    def open_game_location(self):
        """打开游戏文件位置"""
        if hasattr(self, 'selected_game') and self.selected_game:
            try:
                os.startfile(os.path.dirname(self.selected_game['path']))
            except Exception as e:
                messagebox.showerror("错误", f"打开文件位置失败：{str(e)}")

    def remove_game(self):
        """从列表中移除游戏"""
        if hasattr(self, 'selected_game') and self.selected_game:
            if messagebox.askyesno("确认", "确定要从列表中移除这个游戏吗？"):
                game_path = self.selected_game['path']

                # 从数据中移除
                self.games = [game for game in self.games if game['path'] != game_path]
                self.filtered_games = [game for game in self.filtered_games if game['path'] != game_path]

                # 刷新界面
                self.update_game_list()
                self.save_games_to_cache()
            
    def refresh_games(self):
        """刷新游戏列表"""
        self.update_game_list()
        self.progress_var.set("列表已刷新")
        
    def load_games_from_cache(self):
        """从缓存加载游戏列表"""
        try:
            if os.path.exists('games_cache.json'):
                with open('games_cache.json', 'r', encoding='utf-8') as f:
                    self.games = json.load(f)
                    self.filtered_games = self.games.copy()
                    self.update_game_list()
                    self.progress_var.set(f"从缓存加载了 {len(self.games)} 个游戏")
        except Exception as e:
            print(f"加载缓存失败: {e}")
            
    def save_games_to_cache(self):
        """保存游戏列表到缓存"""
        try:
            with open('games_cache.json', 'w', encoding='utf-8') as f:
                json.dump(self.games, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存缓存失败: {e}")
            
    def run(self):
        """运行主程序"""
        self.root.mainloop()


if __name__ == "__main__":
    app = GameLauncher()
    app.run()
