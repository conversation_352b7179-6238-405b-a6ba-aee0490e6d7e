#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏扫描模块
负责扫描系统中的游戏exe文件
"""

import os
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
from game_database import GameDatabase


class GameScanner:
    def __init__(self):
        self.game_db = GameDatabase()
        self.found_games = []
        self.scanned_files = 0
        self.total_files = 0
        self.is_scanning = False
        self.stop_scanning = False
        
        # 扫描配置
        self.max_workers = 4  # 最大线程数
        self.scan_timeout = 300  # 扫描超时时间（秒）
        
        # 排除的目录（提高扫描效率）
        self.exclude_dirs = {
            'windows', 'system32', 'syswow64', 'temp', 'tmp',
            'cache', 'logs', 'log', 'backup', 'recycle.bin',
            '$recycle.bin', 'system volume information',
            'programdata', 'users', 'documents and settings',
            'appdata', 'application data', 'local settings'
        }
        
        # 排除的文件扩展名
        self.exclude_extensions = {
            '.dll', '.sys', '.tmp', '.log', '.txt', '.ini',
            '.cfg', '.dat', '.bak', '.old', '.cache'
        }
    
    def scan_all_drives(self, progress_callback=None):
        """
        扫描所有驱动器查找游戏
        
        Args:
            progress_callback: 进度回调函数
            
        Returns:
            list: 找到的游戏列表
        """
        self.found_games = []
        self.scanned_files = 0
        self.is_scanning = True
        self.stop_scanning = False
        
        try:
            # 获取所有可用驱动器
            drives = self.get_available_drives()
            
            if progress_callback:
                progress_callback(f"开始扫描 {len(drives)} 个驱动器...")
            
            # 使用线程池并行扫描
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交扫描任务
                future_to_drive = {
                    executor.submit(self.scan_drive, drive, progress_callback): drive 
                    for drive in drives
                }
                
                # 收集结果
                for future in as_completed(future_to_drive, timeout=self.scan_timeout):
                    if self.stop_scanning:
                        break
                        
                    drive = future_to_drive[future]
                    try:
                        drive_games = future.result()
                        self.found_games.extend(drive_games)
                        
                        if progress_callback:
                            progress_callback(f"驱动器 {drive} 扫描完成，找到 {len(drive_games)} 个游戏")
                            
                    except Exception as e:
                        if progress_callback:
                            progress_callback(f"扫描驱动器 {drive} 时出错: {str(e)}")
            
            # 去重和排序
            self.found_games = self.deduplicate_games(self.found_games)
            self.found_games.sort(key=lambda x: x['name'].lower())
            
            if progress_callback:
                progress_callback(f"扫描完成！共找到 {len(self.found_games)} 个游戏")
            
        except Exception as e:
            if progress_callback:
                progress_callback(f"扫描过程中出错: {str(e)}")
        finally:
            self.is_scanning = False
        
        return self.found_games
    
    def get_available_drives(self):
        """
        获取所有可用的驱动器
        
        Returns:
            list: 驱动器列表
        """
        drives = []
        
        # 获取所有磁盘分区
        for partition in psutil.disk_partitions():
            # 只扫描固定磁盘（排除光驱、软盘等）
            if 'fixed' in partition.opts or partition.fstype in ['NTFS', 'FAT32', 'exFAT']:
                drives.append(partition.mountpoint)
        
        return drives
    
    def scan_drive(self, drive_path, progress_callback=None):
        """
        扫描单个驱动器
        
        Args:
            drive_path: 驱动器路径
            progress_callback: 进度回调函数
            
        Returns:
            list: 在该驱动器找到的游戏列表
        """
        drive_games = []
        
        try:
            if progress_callback:
                progress_callback(f"正在扫描驱动器: {drive_path}")
            
            # 优先扫描常见游戏目录
            priority_dirs = self.get_priority_scan_dirs(drive_path)
            
            for priority_dir in priority_dirs:
                if self.stop_scanning:
                    break
                    
                if os.path.exists(priority_dir):
                    games = self.scan_directory(priority_dir, progress_callback)
                    drive_games.extend(games)
            
            # 如果优先目录没找到足够游戏，进行全盘扫描
            if len(drive_games) < 5 and not self.stop_scanning:
                if progress_callback:
                    progress_callback(f"对驱动器 {drive_path} 进行深度扫描...")
                
                games = self.scan_directory(drive_path, progress_callback, deep_scan=True)
                drive_games.extend(games)
            
        except Exception as e:
            if progress_callback:
                progress_callback(f"扫描驱动器 {drive_path} 时出错: {str(e)}")
        
        return drive_games
    
    def get_priority_scan_dirs(self, drive_path):
        """
        获取优先扫描的目录列表
        
        Args:
            drive_path: 驱动器路径
            
        Returns:
            list: 优先扫描目录列表
        """
        priority_dirs = []
        
        # 常见游戏安装目录
        common_dirs = [
            'Steam/steamapps/common',
            'Program Files/Steam/steamapps/common',
            'Program Files (x86)/Steam/steamapps/common',
            'Epic Games',
            'Origin Games',
            'Ubisoft',
            'Riot Games',
            'Battle.net',
            'GOG Galaxy/Games',
            'Games',
            'Program Files',
            'Program Files (x86)'
        ]
        
        for dir_name in common_dirs:
            full_path = os.path.join(drive_path, dir_name)
            if os.path.exists(full_path):
                priority_dirs.append(full_path)
        
        return priority_dirs
    
    def scan_directory(self, directory, progress_callback=None, deep_scan=False):
        """
        扫描指定目录
        
        Args:
            directory: 要扫描的目录
            progress_callback: 进度回调函数
            deep_scan: 是否进行深度扫描
            
        Returns:
            list: 找到的游戏列表
        """
        games = []
        
        try:
            for root, dirs, files in os.walk(directory):
                if self.stop_scanning:
                    break
                
                # 过滤排除的目录
                dirs[:] = [d for d in dirs if d.lower() not in self.exclude_dirs]
                
                # 限制深度扫描的层级
                if not deep_scan:
                    level = root.replace(directory, '').count(os.sep)
                    if level > 3:  # 限制扫描深度
                        dirs.clear()
                        continue
                
                # 扫描exe文件
                for file in files:
                    if self.stop_scanning:
                        break
                    
                    if file.lower().endswith('.exe'):
                        exe_path = os.path.join(root, file)
                        
                        # 检查是否可能是游戏
                        if self.game_db.is_likely_game_exe(exe_path, file):
                            game_info = self.game_db.identify_game(exe_path, file)
                            
                            if game_info:
                                games.append(game_info)
                                
                                if progress_callback:
                                    progress_callback(f"找到游戏: {game_info['name']}")
                    
                    self.scanned_files += 1
                    
                    # 定期更新进度
                    if self.scanned_files % 100 == 0 and progress_callback:
                        progress_callback(f"已扫描 {self.scanned_files} 个文件...")
                
        except PermissionError:
            # 忽略权限错误
            pass
        except Exception as e:
            if progress_callback:
                progress_callback(f"扫描目录 {directory} 时出错: {str(e)}")
        
        return games
    
    def deduplicate_games(self, games):
        """
        去除重复的游戏
        
        Args:
            games: 游戏列表
            
        Returns:
            list: 去重后的游戏列表
        """
        seen_paths = set()
        seen_names = set()
        unique_games = []
        
        for game in games:
            # 按路径去重
            if game['path'] not in seen_paths:
                # 按名称去重（保留置信度更高的）
                if game['name'] not in seen_names:
                    unique_games.append(game)
                    seen_names.add(game['name'])
                    seen_paths.add(game['path'])
                else:
                    # 如果名称重复，保留置信度更高的
                    for i, existing_game in enumerate(unique_games):
                        if existing_game['name'] == game['name']:
                            if game['confidence'] > existing_game['confidence']:
                                unique_games[i] = game
                                seen_paths.add(game['path'])
                            break
        
        return unique_games
    
    def stop_scan(self):
        """停止扫描"""
        self.stop_scanning = True
    
    def is_scanning_active(self):
        """检查是否正在扫描"""
        return self.is_scanning
    
    def get_scan_progress(self):
        """
        获取扫描进度
        
        Returns:
            dict: 包含扫描进度信息的字典
        """
        return {
            'scanned_files': self.scanned_files,
            'found_games': len(self.found_games),
            'is_scanning': self.is_scanning
        }
    
    def scan_specific_paths(self, paths, progress_callback=None):
        """
        扫描指定路径列表
        
        Args:
            paths: 要扫描的路径列表
            progress_callback: 进度回调函数
            
        Returns:
            list: 找到的游戏列表
        """
        self.found_games = []
        self.scanned_files = 0
        self.is_scanning = True
        self.stop_scanning = False
        
        try:
            for path in paths:
                if self.stop_scanning:
                    break
                
                if os.path.exists(path):
                    if progress_callback:
                        progress_callback(f"正在扫描: {path}")
                    
                    if os.path.isfile(path) and path.lower().endswith('.exe'):
                        # 单个exe文件
                        file_name = os.path.basename(path)
                        if self.game_db.is_likely_game_exe(path, file_name):
                            game_info = self.game_db.identify_game(path, file_name)
                            if game_info:
                                self.found_games.append(game_info)
                    elif os.path.isdir(path):
                        # 目录
                        games = self.scan_directory(path, progress_callback)
                        self.found_games.extend(games)
            
            # 去重和排序
            self.found_games = self.deduplicate_games(self.found_games)
            self.found_games.sort(key=lambda x: x['name'].lower())
            
            if progress_callback:
                progress_callback(f"扫描完成！共找到 {len(self.found_games)} 个游戏")
                
        except Exception as e:
            if progress_callback:
                progress_callback(f"扫描过程中出错: {str(e)}")
        finally:
            self.is_scanning = False
        
        return self.found_games
