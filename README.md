# 游戏启动器 (Game Launcher)

一个自动扫描全盘游戏并提供图形界面启动的Python应用程序。

## 功能特性

- 🔍 **自动扫描**: 自动扫描全盘查找已安装的游戏
- 🎮 **游戏识别**: 智能识别常见游戏（LOL、PUBG、Steam游戏等）
- 🖼️ **图标显示**: 自动提取并显示游戏图标
- 🚀 **一键启动**: 双击即可启动游戏
- 🔎 **搜索过滤**: 支持游戏名称搜索
- 💾 **缓存机制**: 扫描结果缓存，提高启动速度
- 🎯 **右键菜单**: 支持打开游戏位置、从列表移除等操作

## 系统要求

- Windows 10/11
- Python 3.7+
- 管理员权限（用于访问某些系统目录）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. **启动程序**:
   ```bash
   python main.py
   ```

2. **扫描游戏**:
   - 点击"扫描游戏"按钮开始全盘扫描
   - 扫描过程会显示进度和找到的游戏数量
   - 首次扫描可能需要几分钟时间

3. **启动游戏**:
   - 双击游戏列表中的任意游戏即可启动
   - 或右键选择"启动游戏"

4. **其他功能**:
   - 使用搜索框快速查找游戏
   - 右键菜单提供更多选项
   - 程序会自动保存扫描结果到缓存

## 支持的游戏

程序内置了常见游戏的识别数据库，包括但不限于：

### MOBA游戏
- League of Legends (英雄联盟)
- DOTA 2

### 射击游戏
- PUBG (绝地求生)
- Counter-Strike 2
- Valorant (无畏契约)
- Apex Legends
- Call of Duty: Warzone

### RPG游戏
- Genshin Impact (原神)
- World of Warcraft (魔兽世界)
- The Witcher 3 (巫师3)

### 策略游戏
- Age of Empires II (帝国时代2)
- Civilization VI (文明6)

### 沙盒游戏
- Minecraft (我的世界)
- Grand Theft Auto V (GTA5)

### 游戏平台
- Steam
- Epic Games Launcher
- Origin
- Battle.net
- Uplay

## 项目结构

```
GameOpen/
├── main.py              # 主程序入口
├── game_scanner.py      # 游戏扫描模块
├── game_database.py     # 游戏数据库
├── icon_extractor.py    # 图标提取模块
├── requirements.txt     # 依赖包列表
├── README.md           # 项目说明
└── games_cache.json    # 游戏缓存文件（自动生成）
```

## 技术实现

- **GUI框架**: tkinter (Python内置)
- **图标提取**: PIL/Pillow + Windows API
- **文件扫描**: 多线程并行扫描
- **游戏识别**: 基于文件名、路径、关键词的智能匹配
- **缓存机制**: JSON格式存储扫描结果

## 注意事项

1. **权限要求**: 某些系统目录需要管理员权限才能访问
2. **扫描时间**: 首次全盘扫描可能需要较长时间，建议耐心等待
3. **防病毒软件**: 某些防病毒软件可能会阻止程序访问exe文件
4. **游戏更新**: 游戏更新后可能需要重新扫描

## 故障排除

### 扫描失败
- 确保以管理员权限运行程序
- 检查防病毒软件设置
- 确保磁盘空间充足

### 图标显示异常
- 确保安装了所有依赖包
- 某些游戏可能使用默认图标

### 游戏启动失败
- 检查游戏文件是否完整
- 确保游戏路径正确
- 某些游戏可能需要特定的启动参数

## 开发计划

- [ ] 支持自定义游戏添加
- [ ] 游戏分类和标签功能
- [ ] 游戏运行时间统计
- [ ] 主题和界面自定义
- [ ] 游戏快捷方式创建

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License
