#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包脚本 - 将游戏启动器打包成exe文件
"""

import os
import sys
import subprocess
import shutil

def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        'PIL.ImageDraw',
        'win32api',
        'win32con',
        'win32gui',
        'win32ui',
        'psutil',
        'threading',
        'json',
        'os',
        'sys',
        're'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='游戏启动器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    with open('game_launcher.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 创建规格文件成功")

def build_exe():
    """构建exe文件"""
    print("开始打包exe文件...")
    print("这可能需要几分钟时间，请耐心等待...")
    
    try:
        # 使用PyInstaller打包
        cmd = [
            'pyinstaller',
            '--onefile',  # 打包成单个exe文件
            '--windowed',  # 不显示控制台窗口
            '--name=游戏启动器',  # exe文件名
            '--distpath=dist',  # 输出目录
            '--workpath=build',  # 临时文件目录
            '--specpath=.',  # spec文件目录
            '--clean',  # 清理临时文件
            'main.py'
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ exe文件打包成功！")
            return True
        else:
            print("✗ 打包失败:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"✗ 打包过程中出错: {e}")
        return False

def create_portable_package():
    """创建便携版包"""
    print("\n创建便携版包...")
    
    try:
        # 创建便携版目录
        portable_dir = "GameLauncher_Portable"
        if os.path.exists(portable_dir):
            shutil.rmtree(portable_dir)
        os.makedirs(portable_dir)
        
        # 复制exe文件
        exe_path = "dist/游戏启动器.exe"
        if os.path.exists(exe_path):
            shutil.copy2(exe_path, os.path.join(portable_dir, "游戏启动器.exe"))
            print("✓ 复制exe文件成功")
        else:
            print("✗ 找不到exe文件")
            return False
        
        # 复制README文件
        if os.path.exists("README.md"):
            shutil.copy2("README.md", os.path.join(portable_dir, "使用说明.md"))
            print("✓ 复制说明文件成功")
        
        # 创建使用说明
        usage_text = """游戏启动器使用说明

🎮 功能介绍：
- 自动扫描全盘游戏（LOL、PUBG、Steam游戏等）
- 智能识别常见游戏
- 提取并显示游戏图标
- 双击启动游戏
- 搜索过滤功能

🚀 使用方法：
1. 双击"游戏启动器.exe"启动程序
2. 点击"扫描游戏"按钮开始扫描
3. 等待扫描完成（首次可能需要几分钟）
4. 双击游戏列表中的游戏即可启动

⚠️ 注意事项：
- 首次扫描可能需要较长时间，请耐心等待
- 建议以管理员权限运行以访问所有目录
- 扫描结果会自动保存，下次启动更快

📧 问题反馈：
如有问题请联系开发者
"""
        
        with open(os.path.join(portable_dir, "使用说明.txt"), 'w', encoding='utf-8') as f:
            f.write(usage_text)
        
        print("✓ 创建使用说明成功")
        print(f"✓ 便携版包创建完成: {portable_dir}/")
        
        return True
        
    except Exception as e:
        print(f"✗ 创建便携版包失败: {e}")
        return False

def cleanup():
    """清理临时文件"""
    print("\n清理临时文件...")
    
    cleanup_dirs = ['build', '__pycache__']
    cleanup_files = ['游戏启动器.spec']
    
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✓ 删除临时目录: {dir_name}")
            except Exception as e:
                print(f"- 无法删除目录 {dir_name}: {e}")
    
    for file_name in cleanup_files:
        if os.path.exists(file_name):
            try:
                os.remove(file_name)
                print(f"✓ 删除临时文件: {file_name}")
            except Exception as e:
                print(f"- 无法删除文件 {file_name}: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("游戏启动器 - exe打包工具")
    print("=" * 50)
    
    # 检查必要文件
    required_files = ['main.py', 'game_scanner.py', 'game_database.py', 'icon_extractor.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print("✗ 缺少必要文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    print("✓ 所有必要文件存在")
    
    # 开始打包流程
    steps = [
        ("打包exe文件", build_exe),
        ("创建便携版包", create_portable_package),
        ("清理临时文件", cleanup)
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'-' * 30}")
        print(f"执行: {step_name}")
        print(f"{'-' * 30}")
        
        if not step_func():
            print(f"✗ {step_name} 失败")
            return False
        
        print(f"✓ {step_name} 完成")
    
    print(f"\n{'=' * 50}")
    print("🎉 打包完成！")
    print("📁 exe文件位置: GameLauncher_Portable/游戏启动器.exe")
    print("📦 便携版包: GameLauncher_Portable/")
    print("💡 现在可以将整个 GameLauncher_Portable 文件夹复制到其他电脑使用")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n❌ 打包失败，请检查错误信息")
        input("按回车键退出...")
        sys.exit(1)
    else:
        print("\n✅ 打包成功！")
        input("按回车键退出...")
        sys.exit(0)
