#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏数据库模块
包含常见游戏的识别信息
"""

import os
import re


class GameDatabase:
    def __init__(self):
        # 常见游戏数据库
        self.games_db = {
            # MOBA游戏
            'League of Legends': {
                'exe_names': ['League of Legends.exe', 'LeagueClient.exe', 'LoL.exe'],
                'install_paths': [
                    r'Riot Games\League of Legends',
                    r'腾讯游戏\League of Legends',
                    r'League of Legends'
                ],
                'keywords': ['league', 'lol', '英雄联盟'],
                'publisher': 'Riot Games'
            },
            'DOTA 2': {
                'exe_names': ['dota2.exe'],
                'install_paths': [r'Steam\steamapps\common\dota 2 beta'],
                'keywords': ['dota', 'dota2'],
                'publisher': 'Valve'
            },
            
            # 射击游戏
            'PUBG': {
                'exe_names': ['TslGame.exe', 'PUBG.exe'],
                'install_paths': [
                    r'Steam\steamapps\common\PUBG',
                    r'PUBG'
                ],
                'keywords': ['pubg', '绝地求生', 'battlegrounds'],
                'publisher': 'PUBG Corporation'
            },
            'Counter-Strike 2': {
                'exe_names': ['cs2.exe'],
                'install_paths': [r'Steam\steamapps\common\Counter-Strike Global Offensive'],
                'keywords': ['cs2', 'counter-strike', 'csgo'],
                'publisher': 'Valve'
            },
            'Counter-Strike: Global Offensive': {
                'exe_names': ['csgo.exe'],
                'install_paths': [r'Steam\steamapps\common\Counter-Strike Global Offensive'],
                'keywords': ['csgo', 'counter-strike'],
                'publisher': 'Valve'
            },
            'Valorant': {
                'exe_names': ['VALORANT.exe', 'RiotClientServices.exe'],
                'install_paths': [r'Riot Games\VALORANT'],
                'keywords': ['valorant', '无畏契约'],
                'publisher': 'Riot Games'
            },
            'Apex Legends': {
                'exe_names': ['r5apex.exe'],
                'install_paths': [
                    r'Steam\steamapps\common\Apex Legends',
                    r'Origin Games\Apex'
                ],
                'keywords': ['apex', 'legends'],
                'publisher': 'EA'
            },
            'Call of Duty: Warzone': {
                'exe_names': ['ModernWarfare.exe', 'Warzone.exe'],
                'install_paths': [r'Call of Duty Modern Warfare'],
                'keywords': ['warzone', 'cod', 'call of duty'],
                'publisher': 'Activision'
            },
            
            # RPG游戏
            'Genshin Impact': {
                'exe_names': ['GenshinImpact.exe', 'YuanShen.exe'],
                'install_paths': [
                    r'Genshin Impact\Genshin Impact Game',
                    r'原神\YuanShen_Data'
                ],
                'keywords': ['genshin', '原神', 'impact'],
                'publisher': 'miHoYo'
            },
            'World of Warcraft': {
                'exe_names': ['Wow.exe', 'WowClassic.exe'],
                'install_paths': [
                    r'World of Warcraft\_retail_',
                    r'World of Warcraft\_classic_'
                ],
                'keywords': ['wow', 'warcraft', '魔兽世界'],
                'publisher': 'Blizzard'
            },
            'The Witcher 3': {
                'exe_names': ['witcher3.exe'],
                'install_paths': [
                    r'Steam\steamapps\common\The Witcher 3',
                    r'GOG Galaxy\Games\The Witcher 3'
                ],
                'keywords': ['witcher', '巫师'],
                'publisher': 'CD Projekt RED'
            },
            
            # 策略游戏
            'Age of Empires II': {
                'exe_names': ['AoE2DE_s.exe', 'empires2.exe'],
                'install_paths': [r'Steam\steamapps\common\AoE2DE'],
                'keywords': ['age of empires', 'aoe', '帝国时代'],
                'publisher': 'Microsoft'
            },
            'Civilization VI': {
                'exe_names': ['CivilizationVI.exe', 'CivilizationVI_DX12.exe'],
                'install_paths': [r'Steam\steamapps\common\Sid Meier\'s Civilization VI'],
                'keywords': ['civilization', 'civ', '文明'],
                'publisher': '2K Games'
            },
            
            # 沙盒游戏
            'Minecraft': {
                'exe_names': ['Minecraft.exe', 'MinecraftLauncher.exe'],
                'install_paths': [
                    r'Minecraft',
                    r'Microsoft\Minecraft'
                ],
                'keywords': ['minecraft', '我的世界'],
                'publisher': 'Mojang'
            },
            'Grand Theft Auto V': {
                'exe_names': ['GTA5.exe', 'GTAV.exe'],
                'install_paths': [
                    r'Steam\steamapps\common\Grand Theft Auto V',
                    r'Rockstar Games\Grand Theft Auto V'
                ],
                'keywords': ['gta', 'grand theft auto', '侠盗猎车手'],
                'publisher': 'Rockstar Games'
            },
            
            # 竞速游戏
            'Forza Horizon 5': {
                'exe_names': ['ForzaHorizon5.exe'],
                'install_paths': [r'Steam\steamapps\common\ForzaHorizon5'],
                'keywords': ['forza', 'horizon'],
                'publisher': 'Microsoft'
            },
            
            # 平台客户端
            'Steam': {
                'exe_names': ['Steam.exe'],
                'install_paths': [r'Steam'],
                'keywords': ['steam'],
                'publisher': 'Valve'
            },
            'Epic Games Launcher': {
                'exe_names': ['EpicGamesLauncher.exe'],
                'install_paths': [r'Epic Games\Launcher'],
                'keywords': ['epic', 'launcher'],
                'publisher': 'Epic Games'
            },
            'Origin': {
                'exe_names': ['Origin.exe'],
                'install_paths': [r'Origin'],
                'keywords': ['origin'],
                'publisher': 'EA'
            },
            'Battle.net': {
                'exe_names': ['Battle.net.exe', 'Battle.net Launcher.exe'],
                'install_paths': [r'Battle.net'],
                'keywords': ['battle.net', 'battlenet', 'blizzard'],
                'publisher': 'Blizzard'
            },
            'Uplay': {
                'exe_names': ['upc.exe', 'UbisoftConnect.exe'],
                'install_paths': [r'Ubisoft\Ubisoft Game Launcher'],
                'keywords': ['uplay', 'ubisoft'],
                'publisher': 'Ubisoft'
            }
        }
        
        # 常见游戏exe文件名模式
        self.game_exe_patterns = [
            r'.*game.*\.exe$',
            r'.*launcher.*\.exe$',
            r'.*client.*\.exe$',
            r'.*play.*\.exe$',
            r'.*start.*\.exe$'
        ]
        
        # 常见游戏安装目录
        self.common_game_dirs = [
            'Steam/steamapps/common',
            'Epic Games',
            'Origin Games',
            'Ubisoft',
            'Riot Games',
            'Battle.net',
            'GOG Galaxy/Games',
            'Program Files/Steam',
            'Program Files (x86)/Steam',
            'Games'
        ]
    
    def identify_game(self, exe_path, exe_name):
        """
        识别游戏
        
        Args:
            exe_path: exe文件完整路径
            exe_name: exe文件名
            
        Returns:
            dict: 游戏信息，如果无法识别返回None
        """
        exe_name_lower = exe_name.lower()
        exe_path_lower = exe_path.lower()
        
        # 遍历游戏数据库进行匹配
        for game_name, game_info in self.games_db.items():
            # 检查exe文件名匹配
            for exe in game_info['exe_names']:
                if exe.lower() == exe_name_lower:
                    return {
                        'name': game_name,
                        'path': exe_path,
                        'publisher': game_info.get('publisher', 'Unknown'),
                        'confidence': 1.0
                    }
            
            # 检查关键词匹配
            for keyword in game_info['keywords']:
                if keyword in exe_name_lower or keyword in exe_path_lower:
                    return {
                        'name': game_name,
                        'path': exe_path,
                        'publisher': game_info.get('publisher', 'Unknown'),
                        'confidence': 0.8
                    }
        
        # 如果无法精确匹配，尝试通过路径和文件名推测
        guessed_name = self.guess_game_name(exe_path, exe_name)
        if guessed_name:
            return {
                'name': guessed_name,
                'path': exe_path,
                'publisher': 'Unknown',
                'confidence': 0.5
            }
        
        return None
    
    def guess_game_name(self, exe_path, exe_name):
        """
        通过路径和文件名推测游戏名称
        
        Args:
            exe_path: exe文件完整路径
            exe_name: exe文件名
            
        Returns:
            str: 推测的游戏名称
        """
        # 从路径中提取可能的游戏名称
        path_parts = exe_path.replace('\\', '/').split('/')
        
        # 查找可能包含游戏名称的路径部分
        for part in reversed(path_parts[:-1]):  # 排除文件名本身
            if part and not self.is_system_dir(part):
                # 清理路径名称
                cleaned_name = self.clean_name(part)
                if len(cleaned_name) > 3:  # 避免太短的名称
                    return cleaned_name
        
        # 如果路径中找不到，尝试从exe文件名推测
        exe_base = os.path.splitext(exe_name)[0]
        cleaned_exe_name = self.clean_name(exe_base)
        
        if len(cleaned_exe_name) > 3:
            return cleaned_exe_name
        
        return None
    
    def is_system_dir(self, dir_name):
        """
        判断是否为系统目录
        
        Args:
            dir_name: 目录名称
            
        Returns:
            bool: 是否为系统目录
        """
        system_dirs = {
            'program files', 'program files (x86)', 'windows', 'system32',
            'bin', 'common', 'steamapps', 'games', 'launcher', 'client'
        }
        return dir_name.lower() in system_dirs
    
    def clean_name(self, name):
        """
        清理名称，移除版本号、特殊字符等
        
        Args:
            name: 原始名称
            
        Returns:
            str: 清理后的名称
        """
        # 移除常见的后缀
        suffixes_to_remove = [
            r'\s*\d+\.\d+.*',  # 版本号
            r'\s*v\d+.*',      # v1.0等
            r'\s*\(.*\)',      # 括号内容
            r'\s*\[.*\]',      # 方括号内容
            r'\s*-.*',         # 破折号后内容
            r'\s*_.*'          # 下划线后内容（部分情况）
        ]
        
        cleaned = name
        for suffix_pattern in suffixes_to_remove:
            cleaned = re.sub(suffix_pattern, '', cleaned, flags=re.IGNORECASE)
        
        # 替换下划线和破折号为空格
        cleaned = re.sub(r'[_-]', ' ', cleaned)
        
        # 移除多余空格
        cleaned = ' '.join(cleaned.split())
        
        # 首字母大写
        cleaned = cleaned.title()
        
        return cleaned.strip()
    
    def is_likely_game_exe(self, exe_path, exe_name):
        """
        判断exe文件是否可能是游戏（更严格的过滤）

        Args:
            exe_path: exe文件完整路径
            exe_name: exe文件名

        Returns:
            bool: 是否可能是游戏exe
        """
        exe_name_lower = exe_name.lower()
        exe_path_lower = exe_path.lower()

        # 首先检查是否是已知游戏
        for game_name, game_info in self.games_db.items():
            # 检查exe文件名匹配
            for exe in game_info['exe_names']:
                if exe.lower() == exe_name_lower:
                    return True

            # 检查关键词匹配
            for keyword in game_info['keywords']:
                if keyword in exe_name_lower or keyword in exe_path_lower:
                    return True

        # 排除明显的系统文件、工具和非游戏软件
        exclude_patterns = [
            r'unins\d+\.exe',     # 卸载程序
            r'setup\.exe',        # 安装程序
            r'installer\.exe',    # 安装程序
            r'updater\.exe',      # 更新程序
            r'launcher\.exe',     # 启动器（除非是游戏启动器）
            r'crash.*\.exe',      # 崩溃报告
            r'error.*\.exe',      # 错误报告
            r'redist.*\.exe',     # 运行库
            r'vcredist.*\.exe',   # VC运行库
            r'directx.*\.exe',    # DirectX
            r'dotnet.*\.exe',     # .NET
            r'java.*\.exe',       # Java
            r'adobe.*\.exe',      # Adobe软件
            r'microsoft.*\.exe',  # Microsoft软件
            r'nvidia.*\.exe',     # NVIDIA软件
            r'intel.*\.exe',      # Intel软件
            r'amd.*\.exe',        # AMD软件
            r'driver.*\.exe',     # 驱动程序
            r'service.*\.exe',    # 服务程序
            r'helper.*\.exe',     # 辅助程序
            r'monitor.*\.exe',    # 监控程序
            r'manager.*\.exe',    # 管理程序
            r'config.*\.exe',     # 配置程序
            r'settings.*\.exe',   # 设置程序
        ]

        for pattern in exclude_patterns:
            if re.match(pattern, exe_name_lower):
                return False

        # 排除系统目录和程序文件目录中的大部分exe
        system_paths = [
            'windows', 'system32', 'syswow64', 'program files', 'program files (x86)',
            'programdata', 'users', 'appdata', 'temp', 'tmp'
        ]

        # 如果在系统目录中，除非是已知游戏目录，否则排除
        in_system_path = any(sys_path in exe_path_lower for sys_path in system_paths)
        in_game_path = any(game_dir.lower() in exe_path_lower for game_dir in self.common_game_dirs)

        if in_system_path and not in_game_path:
            return False

        # 检查文件大小（游戏exe通常较大，至少5MB）
        try:
            file_size = os.path.getsize(exe_path)
            if file_size < 5 * 1024 * 1024:  # 小于5MB
                return False
        except:
            return False

        # 如果在游戏相关目录中，更可能是游戏
        if in_game_path:
            return True

        # 最后的检查：文件名是否包含游戏相关关键词
        game_keywords = [
            'game', 'play', 'launcher', 'client', 'start', 'run',
            '游戏', '启动', '客户端', '运行'
        ]

        if any(keyword in exe_name_lower for keyword in game_keywords):
            return True

        return False
    
    def get_all_known_games(self):
        """
        获取所有已知游戏列表
        
        Returns:
            list: 游戏名称列表
        """
        return list(self.games_db.keys())
    
    def get_game_info(self, game_name):
        """
        获取指定游戏的详细信息
        
        Args:
            game_name: 游戏名称
            
        Returns:
            dict: 游戏信息
        """
        return self.games_db.get(game_name)
